<script setup lang="ts">
import inlineCKEditor from '@/components/editors/VeditorInline.vue';
import classicCKEditor from '@/components/editors/Veditor.vue';
import CmpButton from '@/components/CmpButton.vue';
import { editBaseInfoApi } from '@/apis/path/klg';
import type { params2EditBaseInfo } from '@/apis/path/klg';
import { KlgType, KlgTypeDict } from '@/utils/constant';
import { KlgDetail } from '@/utils/type';
import { ElMessage, FormInstance } from 'element-plus';
import { ref, watch, nextTick } from 'vue';
import router from '@/router';
import { useRoute } from 'vue-router';
import { convertLanguageMathToScript, convertImgTagLongUrls } from '@/utils/latexUtils';
const route = useRoute();
const props = defineProps({
  showStep3: Boolean,
  detail: Object as () => KlgDetail
});
const emits = defineEmits(['step', 'showStep3', 'refresh']);
const infoFormRef = ref<FormInstance>();
const blankInfoForm = ref({
  klgCode: '',
  klgType: '',
  klgName: '',
  synonymList: [] as String[],
  note: ''
});
const infoForm = ref(blankInfoForm); // 基础信息表单
const curKlgCode = ref();
const editorTitle = ref();
const editorNote = ref();

// 处理选择改变
const handleChangeOption = () => {
  if (KlgTypeDict[infoForm.value.klgType] === KlgType.Principles) {
    emits('showStep3', true);
  } else {
    emits('showStep3', false);
  }
};
// 处理添加同义词
const handleAddSynonym = () => {
  infoForm.value.synonymList.push('');
};
// 处理移除同义词
const handleRemoveSynonym = (target: string) => {
  const index = infoForm.value.synonymList.indexOf(target);
  if (index !== -1) {
    infoForm.value.synonymList.splice(index, 1);
  }
};
// 处理取消
const handleCancel = () => {
  const curNode = sessionStorage.getItem('currentNode');
  if (curNode) {
    const node = JSON.parse(curNode);
    router.push(`klg/maintain?areaCode=${node.areaCode}&label=${node.label}`);
  } else {
    const defaultNode = sessionStorage.getItem('defaultNode');
    if (defaultNode) {
      const node = JSON.parse(defaultNode);
      router.push(`klg/maintain?areaCode=${node.areaCode}&label=${node.label}`);
    } else {
      router.push(`klg/maintain`);
    }
  }
};
// 处理打开编者规范
const handleOpenRule = () => {
  window.open('/rule', '_blank');
};
// 处理下一步
const handleNextStep = () => {
  infoFormRef.value?.validate((valid) => {
    if (valid) {
      handleSave().then((result) => {
        if (result) {
          emits('step', 1);
          router.replace({
            query: {
              ...route.query,
              klgCode: curKlgCode.value
            }
          });
          const data = {
            klgCode: curKlgCode.value,
            step: 1
          };
          emits('refresh', data);
        }
      });
    }
  });
};
// 处理保存
const handleSave = async (): Promise<boolean> => {
  return new Promise<boolean>((resolve) => {
    infoFormRef.value?.validateField('klgName', async (valid) => {
      if (!valid) {
        resolve(false);
        return;
      }

      const area = sessionStorage.getItem('area');
      const params: params2EditBaseInfo = {
        klgCode: infoForm.value.klgCode,
        sortId: KlgTypeDict[infoForm.value.klgType],
        title: infoForm.value.klgName,
        sysTitles: infoForm.value.synonymList.join('@@'),
        // notice: infoForm.value.note,
        notice: convertLanguageMathToScript(convertImgTagLongUrls(editorNote.value.getHtml())),
        areaCodes: area ? [area] : []
      };

      try {
        const res = await editBaseInfoApi(params);
        if (res.success) {
          curKlgCode.value = res.data.klgCode;
          router.replace({
            query: {
              ...route.query,
              klgCode: curKlgCode.value
            }
          });
          ElMessage.success('保存成功');
          resolve(true);
        } else {
          ElMessage.error(res.message);
          resolve(false);
        }
      } catch (error) {
        console.error(`Error: ${error}`);
        resolve(false);
      }
    });
  });
};
watch(
  () => props.detail,
  async () => {
    if (props.detail) {
      infoForm.value.klgCode = props.detail.klgCode;
      infoForm.value.klgName = props.detail.title;
      infoForm.value.klgType = props.detail.sortTitle;

      // 等待 DOM 更新，确保 editorNote 组件完全初始化
      await nextTick();

      // 转换 HTML 内容为 Markdown 并赋值给编辑器
      const htmlContent = props.detail.notice ? props.detail.notice : '';

      // 等待 vditor 完全就绪的函数
      const waitForVditorReady = async (maxWaitTime = 3000) => {
        const startTime = Date.now();
        while (Date.now() - startTime < maxWaitTime) {
          if (editorNote.value?.html2Md) {
            const testResult = editorNote.value.html2Md('');
            if (testResult !== null && testResult !== undefined) {
              console.log('vditor 已就绪');
              return true;
            }
          }
          await new Promise(resolve => setTimeout(resolve, 100)); // 等待100ms
        }
        console.log('vditor 等待超时');
        return false;
      };

      // 等待 vditor 就绪后再转换
      const isVditorReady = await waitForVditorReady();
      let convertedContent = '';

      if (isVditorReady && htmlContent) {
        convertedContent = editorNote.value?.html2Md?.(htmlContent) || '';
        console.log('转换成功:', convertedContent);
      } else {
        console.log('使用原内容:', htmlContent);
      }

      infoForm.value.note = convertedContent || htmlContent;

      if (props.detail.sysTitles === '' || !props.detail.sysTitles) {
        infoForm.value.synonymList = [];
      } else {
        infoForm.value.synonymList = props.detail.sysTitles.split('@@');
      }
    }
  },
  { deep: true, immediate: true }
);
</script>

<template>
  <div class="wrapper">
    <div class="header-wrapper">
      <el-steps class="header-steps" :active="0" finish-status="success" align-center>
        <el-step title="基础信息" />
        <el-step title="内容信息" />
        <el-step v-if="props.showStep3" title="论证信息" />
      </el-steps>
      <span class="header-tips" @click="handleOpenRule">编者规范</span>
    </div>
    <div class="line"></div>
    <div class="form-container">
      <el-form ref="infoFormRef" label-postion="right" label-width="100px" :model="infoForm">
        <el-form-item
          label="知识类型"
          prop="klgType"
          required
          :rules="{
            required: true,
            message: '请输入知识类型',
            trigger: 'blur'
          }"
        >
          <el-select
            v-model="infoForm.klgType"
            style="width: 400px; margin-top: 7px"
            @change="handleChangeOption"
          >
            <el-option
              v-for="(key, value) in KlgTypeDict"
              :key="key"
              :value="value"
              class="primary"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="知识名称"
          prop="klgName"
          required
          :rules="{
            required: true,
            message: '请输入知识名称',
            trigger: 'blur'
          }"
        >
          <inlineCKEditor
            v-model="infoForm.klgName"
            :disabled="false"
            :width="1036"
            :height="36"
            ref="editorTitle"
          ></inlineCKEditor>
        </el-form-item>
        <el-form-item label="同义词" prop="synonymList">
          <el-form-item>
            <span class="add-block" @click="handleAddSynonym">
              <img src="@/assets/image/add.svg" /><span class="add-text">添加同义词</span>
            </span>
          </el-form-item>
          <div
            v-for="(item, index) in infoForm.synonymList"
            :key="index"
            style="width: 100%; margin-bottom: 14px"
          >
            <el-form-item
              style="width: 100%"
              :prop="`synonymList.${index}`"
              :rules="{
                required: true,
                message: '请输入同义词',
                trigger: 'blur'
              }"
            >
              <div style="width: 100%; display: flex">
                <inlineCKEditor
                  v-model="infoForm.synonymList[index]"
                  placeholder="请输入同义词"
                  :disabled="false"
                  :height="36"
                  style="margin-top: 7px"
                ></inlineCKEditor>
                <span class="rm-block" @click="handleRemoveSynonym(item.toString())"
                  ><img src="@/assets/image/rm.svg"
                /></span>
              </div>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="编者笔记">
          <div style="width: 98%; height: 400px" class="note-block">
            <div class="add-block">
              <img src="@/assets/image/klg/u2852.svg" />
              <span class="add-text">画图</span>
            </div>
            <classicCKEditor
              v-model="infoForm.note"
              :height="380"
              ref="editorNote"
            ></classicCKEditor>
          </div>
        </el-form-item>
      </el-form>
      <div class="footer">
        <CmpButton type="info" @click="handleCancel">取消</CmpButton>
        <CmpButton type="primary" @click="handleSave">存草稿</CmpButton>
        <CmpButton type="primary" @click="handleNextStep">下一步</CmpButton>
      </div>
    </div>
  </div>
</template>
<style scoped>
.wrapper {
  width: 1200px;
  min-height: 750px;
  font-family: var(--text-family);
  background-color: white;
  .header-wrapper {
    width: 100%;
    display: flex;
    justify-content: center;
    position: relative;
    /* el-steps 进行状态 */
    ::v-deep(.is-process) {
      color: var(--color-black);
      font-weight: 400;
      font-size: 14px;
    }
    /* el-steps 等待状态 */
    ::v-deep(.is-wait) {
      color: var(--color-invalid);
      font-weight: 400;
      font-size: 14px;
    }
    .header-steps {
      --el-color-success: var(--color-black);
      margin-top: 10px;
      width: 400px;
    }
    .header-tips {
      color: var(--color-primary);
      font-size: 12px;
      cursor: pointer;
      position: absolute;
      right: 40px;
      bottom: 5px;
      &:hover {
        font-weight: 600;
      }
    }
  }
  .form-container {
    padding: 10px;
    width: 98%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .note-block {
      height: 400px;
    }
    .add-block {
      display: flex;
      align-items: center;
      cursor: pointer;
      .add-text {
        margin-left: 5px;
        color: var(--color-primary);
        font-size: 12px;
      }
    }
    .rm-block {
      margin-left: 5px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    .footer {
      width: 100%;
      display: flex;
      justify-content: center;
      gap: 40px;
    }
  }
}
.line {
  width: 100%;
  height: 1px;
  background-color: var(--color-boxborder);
  margin-bottom: 15px;
}
</style>
